{"version": 3, "sources": ["../../../../src/shared/lib/router/adapters.tsx"], "names": ["React", "useMemo", "useRef", "PathnameContext", "isDynamicRoute", "asPathToSearchParams", "getRouteRegex", "adaptForAppRouterInstance", "pagesRouter", "back", "forward", "refresh", "reload", "fastRefresh", "push", "href", "scroll", "undefined", "replace", "prefetch", "adaptForSearchParams", "router", "isReady", "query", "URLSearchParams", "<PERSON><PERSON><PERSON>", "adaptForPathParams", "pathParams", "routeRegex", "pathname", "keys", "Object", "groups", "key", "PathnameContextProviderAdapter", "children", "props", "ref", "isAutoExport", "value", "current", "<PERSON><PERSON><PERSON><PERSON>", "url", "URL", "_", "Provider"], "mappings": ";AAIA,OAAOA,SAASC,OAAO,EAAEC,MAAM,QAAQ,QAAO;AAC9C,SAASC,eAAe,QAAQ,yCAAwC;AACxE,SAASC,cAAc,QAAQ,UAAS;AACxC,SAASC,oBAAoB,QAAQ,mCAAkC;AACvE,SAASC,aAAa,QAAQ,sBAAqB;AAEnD,wEAAwE,GACxE,OAAO,SAASC,0BACdC,WAAuB;IAEvB,OAAO;QACLC;YACED,YAAYC,IAAI;QAClB;QACAC;YACEF,YAAYE,OAAO;QACrB;QACAC;YACEH,YAAYI,MAAM;QACpB;QACAC,gBAAe;QACfC,MAAKC,IAAI,EAAE;YAAA,IAAA,EAAEC,MAAM,EAAE,GAAV,mBAAa,CAAC,IAAd;YACT,KAAKR,YAAYM,IAAI,CAACC,MAAME,WAAW;gBAAED;YAAO;QAClD;QACAE,SAAQH,IAAI,EAAE;YAAA,IAAA,EAAEC,MAAM,EAAE,GAAV,mBAAa,CAAC,IAAd;YACZ,KAAKR,YAAYU,OAAO,CAACH,MAAME,WAAW;gBAAED;YAAO;QACrD;QACAG,UAASJ,IAAI;YACX,KAAKP,YAAYW,QAAQ,CAACJ;QAC5B;IACF;AACF;AAEA;;;;;CAKC,GACD,OAAO,SAASK,qBACdC,MAAwD;IAExD,IAAI,CAACA,OAAOC,OAAO,IAAI,CAACD,OAAOE,KAAK,EAAE;QACpC,OAAO,IAAIC;IACb;IAEA,OAAOnB,qBAAqBgB,OAAOI,MAAM;AAC3C;AAEA,OAAO,SAASC,mBACdL,MAAqE;IAErE,IAAI,CAACA,OAAOC,OAAO,IAAI,CAACD,OAAOE,KAAK,EAAE;QACpC,OAAO;IACT;IACA,MAAMI,aAAqB,CAAC;IAC5B,MAAMC,aAAatB,cAAce,OAAOQ,QAAQ;IAChD,MAAMC,OAAOC,OAAOD,IAAI,CAACF,WAAWI,MAAM;IAC1C,KAAK,MAAMC,OAAOH,KAAM;QACtBH,UAAU,CAACM,IAAI,GAAGZ,OAAOE,KAAK,CAACU,IAAI;IACrC;IACA,OAAON;AACT;AAEA,OAAO,SAASO,+BAA+B,KAO7C;IAP6C,IAAA,EAC7CC,QAAQ,EACRd,MAAM,EACN,GAAGe,OAIH,GAP6C;IAQ7C,MAAMC,MAAMnC,OAAOkC,MAAME,YAAY;IACrC,MAAMC,QAAQtC,QAAQ;QACpB,wEAAwE;QACxE,2EAA2E;QAC3E,iDAAiD;QACjD,MAAMqC,eAAeD,IAAIG,OAAO;QAChC,IAAIF,cAAc;YAChBD,IAAIG,OAAO,GAAG;QAChB;QAEA,sEAAsE;QACtE,qDAAqD;QACrD,IAAIpC,eAAeiB,OAAOQ,QAAQ,GAAG;YACnC,yEAAyE;YACzE,uEAAuE;YACvE,MAAM;YACN,sFAAsF;YACtF,IAAIR,OAAOoB,UAAU,EAAE;gBACrB,OAAO;YACT;YAEA,oEAAoE;YACpE,wEAAwE;YACxE,mEAAmE;YACnE,mBAAmB;YACnB,0EAA0E;YAC1E,IAAIH,gBAAgB,CAACjB,OAAOC,OAAO,EAAE;gBACnC,OAAO;YACT;QACF;QAEA,2EAA2E;QAC3E,2EAA2E;QAC3E,2BAA2B;QAC3B,kEAAkE;QAClE,IAAIoB;QACJ,IAAI;YACFA,MAAM,IAAIC,IAAItB,OAAOI,MAAM,EAAE;QAC/B,EAAE,OAAOmB,GAAG;YACV,kDAAkD;YAClD,OAAO;QACT;QAEA,OAAOF,IAAIb,QAAQ;IACrB,GAAG;QAACR,OAAOI,MAAM;QAAEJ,OAAOoB,UAAU;QAAEpB,OAAOC,OAAO;QAAED,OAAOQ,QAAQ;KAAC;IAEtE,qBACE,KAAC1B,gBAAgB0C,QAAQ;QAACN,OAAOA;kBAC9BJ;;AAGP"}