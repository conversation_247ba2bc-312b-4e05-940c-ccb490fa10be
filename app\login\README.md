# Login Page - QR Student Attendance System

## Overview
Modern, responsive login page for the QR-Code Based Student Attendance and Monitoring System for Tanauan School of Arts and Trade.

## Features

### 🎨 Design
- **Modern UI**: Clean, professional design with shadcn/ui components
- **School Branding**: Integrated TSAT branding and school information
- **Responsive Layout**: Optimized for desktop and tablet devices
- **Dark/Light Mode**: Theme toggle in top-right corner
- **Subtle Animations**: Smooth transitions and hover effects

### 🔐 Authentication
- **Form Validation**: Real-time validation using react-hook-form and Zod
- **Error Handling**: User-friendly error messages
- **Loading States**: Visual feedback during login process
- **Remember Me**: Option to persist login session
- **Password Visibility**: Toggle to show/hide password

### ♿ Accessibility
- **ARIA Labels**: Proper accessibility attributes
- **Keyboard Navigation**: Full keyboard support
- **Screen Reader Support**: Semantic HTML structure
- **Focus Management**: Clear focus indicators

### 🎯 Form Fields
- **Username/Employee ID**: Primary identifier field
- **Password**: Secure password input with visibility toggle
- **Remember Me**: Checkbox for session persistence
- **Forgot Password**: Link for password recovery

## Demo Credentials
For testing purposes, use:
- **Username**: `admin`
- **Password**: `password`

## Technical Implementation

### Dependencies
- **react-hook-form**: Form state management and validation
- **@hookform/resolvers**: Zod resolver for validation
- **zod**: Schema validation
- **shadcn/ui**: UI component library
- **lucide-react**: Icon library
- **next-themes**: Theme management

### Components Used
- `Card`, `CardHeader`, `CardTitle`, `CardDescription`, `CardContent`
- `Form`, `FormField`, `FormItem`, `FormLabel`, `FormControl`, `FormMessage`
- `Input`, `Label`, `Button`, `Checkbox`
- `ThemeToggle`

### Styling
- **Tailwind CSS**: Utility-first CSS framework
- **Educational Colors**: Custom blue and green color palette
- **Backdrop Blur**: Modern glass-morphism effect
- **Gradient Backgrounds**: Subtle educational-themed gradients

## File Structure
```
app/login/
├── page.tsx          # Main login page component
├── layout.tsx        # Login-specific layout
└── README.md         # This documentation
```

## Usage
Navigate to `/login` to access the login page. The page is designed to be the entry point for the QR Student Attendance System.

## Future Enhancements
- [ ] OAuth integration (Google, Microsoft)
- [ ] Two-factor authentication
- [ ] Password strength indicator
- [ ] Forgot password functionality
- [ ] Account lockout protection
- [ ] Login attempt logging
