{"version": 3, "sources": ["../../../src/shared/lib/app-dynamic.tsx"], "names": ["React", "Loadable", "dynamic", "dynamicOptions", "options", "mergedOptions", "loadableOptions", "loading", "error", "isLoading", "past<PERSON>elay", "process", "env", "NODE_ENV", "p", "message", "br", "stack", "loader", "modules", "loadableGenerated"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AACzB,OAAOC,cAAc,0BAAyB;AAgC9C,eAAe,SAASC,QACtBC,cAA6C,EAC7CC,OAA2B;QAmChBC;IAjCX,IAAIC,kBAAsC;QACxC,wDAAwD;QACxDC,SAAS;gBAAC,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;YACvC,IAAI,CAACA,WAAW,OAAO;YACvB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAIJ,WAAW;oBACb,OAAO;gBACT;gBACA,IAAID,OAAO;oBACT,qBACE,MAACM;;4BACEN,MAAMO,OAAO;0CACd,KAACC;4BACAR,MAAMS,KAAK;;;gBAGlB;YACF;YACA,OAAO;QACT;IACF;IAEA,IAAI,OAAOd,mBAAmB,YAAY;QACxCG,gBAAgBY,MAAM,GAAGf;IAC3B;IAEA,MAAME,gBAAgB;QACpB,GAAGC,eAAe;QAClB,GAAGF,OAAO;IACZ;IAEA,OAAOH,SAAS;QACd,GAAGI,aAAa;QAChBc,OAAO,GAAEd,mCAAAA,cAAce,iBAAiB,qBAA/Bf,iCAAiCc,OAAO;IACnD;AACF"}