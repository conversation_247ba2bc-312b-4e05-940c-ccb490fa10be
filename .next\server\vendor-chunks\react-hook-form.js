"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   createFormControl: () => (/* binding */ createFormControl),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\nvar isCheckBoxInput = (element) => element.type === 'checkbox';\n\nvar isDateObject = (value) => value instanceof Date;\n\nvar isNullOrUndefined = (value) => value == null;\n\nconst isObjectType = (value) => typeof value === 'object';\nvar isObject = (value) => !isNullOrUndefined(value) &&\n    !Array.isArray(value) &&\n    isObjectType(value) &&\n    !isDateObject(value);\n\nvar getEventValue = (event) => isObject(event) && event.target\n    ? isCheckBoxInput(event.target)\n        ? event.target.checked\n        : event.target.value\n    : event;\n\nvar getNodeParentName = (name) => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\n\nvar isPlainObject = (tempObject) => {\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return (isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf'));\n};\n\nvar isWeb = typeof window !== 'undefined' &&\n    typeof window.HTMLElement !== 'undefined' &&\n    typeof document !== 'undefined';\n\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    }\n    else if (!(isWeb && (data instanceof Blob || isFileListInstance)) &&\n        (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        }\n        else {\n            for (const key in data) {\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    }\n    else {\n        return data;\n    }\n    return copy;\n}\n\nvar isKey = (value) => /^\\w*$/.test(value);\n\nvar isUndefined = (val) => val === undefined;\n\nvar compact = (value) => Array.isArray(value) ? value.filter(Boolean) : [];\n\nvar stringToPath = (input) => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n\nvar get = (object, path, defaultValue) => {\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = (isKey(path) ? [path] : stringToPath(path)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object\n        ? isUndefined(object[path])\n            ? defaultValue\n            : object[path]\n        : result;\n};\n\nvar isBoolean = (value) => typeof value === 'boolean';\n\nvar set = (object, path, value) => {\n    let index = -1;\n    const tempPath = isKey(path) ? [path] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while (++index < length) {\n        const key = tempPath[index];\n        let newValue = value;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue =\n                isObject(objValue) || Array.isArray(objValue)\n                    ? objValue\n                    : !isNaN(+tempPath[index + 1])\n                        ? []\n                        : {};\n        }\n        if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\n\nconst EVENTS = {\n    BLUR: 'blur',\n    FOCUS_OUT: 'focusout',\n    CHANGE: 'change',\n};\nconst VALIDATION_MODE = {\n    onBlur: 'onBlur',\n    onChange: 'onChange',\n    onSubmit: 'onSubmit',\n    onTouched: 'onTouched',\n    all: 'all',\n};\nconst INPUT_VALIDATION_RULES = {\n    max: 'max',\n    min: 'min',\n    maxLength: 'maxLength',\n    minLength: 'minLength',\n    pattern: 'pattern',\n    required: 'required',\n    validate: 'validate',\n};\n\nconst HookFormContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nHookFormContext.displayName = 'HookFormContext';\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = (props) => {\n    const { children, ...data } = props;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, { value: data }, children));\n};\n\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true) => {\n    const result = {\n        defaultValues: control._defaultValues,\n    };\n    for (const key in formState) {\n        Object.defineProperty(result, key, {\n            get: () => {\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            },\n        });\n    }\n    return result;\n};\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    });\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n            !disabled &&\n                updateFormState({\n                    ...control._formState,\n                    ...formState,\n                });\n        },\n    }), [name, disabled, exact]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [control]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);\n}\n\nvar isString = (value) => typeof value === 'string';\n\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName) => (isGlobal && _names.watch.add(fieldName),\n            get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n\nvar isPrimitive = (value) => isNullOrUndefined(value) || !isObjectType(value);\n\nfunction deepEqual(object1, object2, _internal_visited = new WeakSet()) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n        return true;\n    }\n    _internal_visited.add(object1);\n    _internal_visited.add(object2);\n    for (const key of keys1) {\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== 'ref') {\n            const val2 = object2[key];\n            if ((isDateObject(val1) && isDateObject(val2)) ||\n                (isObject(val1) && isObject(val2)) ||\n                (Array.isArray(val1) && Array.isArray(val2))\n                ? !deepEqual(val1, val2, _internal_visited)\n                : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact, compute, } = props || {};\n    const _defaultValue = react__WEBPACK_IMPORTED_MODULE_0__.useRef(defaultValue);\n    const _compute = react__WEBPACK_IMPORTED_MODULE_0__.useRef(compute);\n    const _computeFormValues = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    _compute.current = compute;\n    const defaultValueMemo = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => control._getWatch(name, _defaultValue.current), [control, name]);\n    const [value, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(_compute.current ? _compute.current(defaultValueMemo) : defaultValueMemo);\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: {\n            values: true,\n        },\n        exact,\n        callback: (formState) => {\n            if (!disabled) {\n                const formValues = generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current);\n                if (_compute.current) {\n                    const computedFormValues = _compute.current(formValues);\n                    if (!deepEqual(computedFormValues, _computeFormValues.current)) {\n                        updateValue(computedFormValues);\n                        _computeFormValues.current = computedFormValues;\n                    }\n                }\n                else {\n                    updateValue(formValues);\n                }\n            }\n        },\n    }), [control, disabled, name, exact]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._removeUnmounted());\n    return value;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister, defaultValue, } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const defaultValueMemo = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => get(control._formValues, name, get(control._defaultValues, name, defaultValue)), [control, name, defaultValue]);\n    const value = useWatch({\n        control,\n        name,\n        defaultValue: defaultValueMemo,\n        exact: true,\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true,\n    });\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value,\n        ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }));\n    _props.current = props;\n    const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => Object.defineProperties({}, {\n        invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n        },\n        isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n        },\n        isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n        },\n        isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n        },\n        error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n        },\n    }), [formState, name]);\n    const onChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => _registerProps.current.onChange({\n        target: {\n            value: getEventValue(event),\n            name: name,\n        },\n        type: EVENTS.CHANGE,\n    }), [name]);\n    const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => _registerProps.current.onBlur({\n        target: {\n            value: get(control._formValues, name),\n            name: name,\n        },\n        type: EVENTS.BLUR,\n    }), [name, control._formValues]);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((elm) => {\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: () => elm.focus && elm.focus(),\n                select: () => elm.select && elm.select(),\n                setCustomValidity: (message) => elm.setCustomValidity(message),\n                reportValidity: () => elm.reportValidity(),\n            };\n        }\n    }, [control._fields, name]);\n    const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        name,\n        value,\n        ...(isBoolean(disabled) || formState.disabled\n            ? { disabled: formState.disabled || disabled }\n            : {}),\n        onChange,\n        onBlur,\n        ref,\n    }), [name, disabled, formState.disabled, onChange, onBlur, ref, value]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        control.register(name, {\n            ..._props.current.rules,\n            ...(isBoolean(_props.current.disabled)\n                ? { disabled: _props.current.disabled }\n                : {}),\n        });\n        const updateMounted = (name, value) => {\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value);\n            }\n        }\n        !isArrayField && control.register(name);\n        return () => {\n            (isArrayField\n                ? _shouldUnregisterField && !control._state.action\n                : _shouldUnregisterField)\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, isArrayField, shouldUnregister]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        control._setDisabledField({\n            disabled,\n            name,\n        });\n    }, [disabled, name, control]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        field,\n        formState,\n        fieldState,\n    }), [field, formState, fieldState]);\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = (props) => props.render(useController(props));\n\nconst flatten = (obj) => {\n    const output = {};\n    for (const key of Object.keys(obj)) {\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)) {\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        }\n        else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\n\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event) => {\n        let hasError = false;\n        let type = '';\n        await control.handleSubmit(async (data) => {\n            const formData = new FormData();\n            let formDataJson = '';\n            try {\n                formDataJson = JSON.stringify(data);\n            }\n            catch (_a) { }\n            const flattenFormValues = flatten(control._formValues);\n            for (const key in flattenFormValues) {\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson,\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers['Content-Type'],\n                        encType,\n                    ].some((value) => value && value.includes('json'));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...(encType && encType !== 'multipart/form-data'\n                                ? { 'Content-Type': encType }\n                                : {}),\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData,\n                    });\n                    if (response &&\n                        (validateStatus\n                            ? !validateStatus(response.status)\n                            : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({ response });\n                        type = String(response.status);\n                    }\n                    else {\n                        onSuccess && onSuccess({ response });\n                    }\n                }\n                catch (error) {\n                    hasError = true;\n                    onError && onError({ error });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false,\n            });\n            props.control.setError('root.server', {\n                type,\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        setMounted(true);\n    }, []);\n    return render ? (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit,\n    }))) : (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", { noValidate: mounted, action: action, method: method, encType: encType, onSubmit: submit, ...rest }, children));\n}\n\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n            ...(errors[name] && errors[name].types ? errors[name].types : {}),\n            [type]: message || true,\n        },\n    }\n    : {};\n\nvar convertToArrayPayload = (value) => (Array.isArray(value) ? value : [value]);\n\nvar createSubject = () => {\n    let _observers = [];\n    const next = (value) => {\n        for (const observer of _observers) {\n            observer.next && observer.next(value);\n        }\n    };\n    const subscribe = (observer) => {\n        _observers.push(observer);\n        return {\n            unsubscribe: () => {\n                _observers = _observers.filter((o) => o !== observer);\n            },\n        };\n    };\n    const unsubscribe = () => {\n        _observers = [];\n    };\n    return {\n        get observers() {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe,\n    };\n};\n\nvar isEmptyObject = (value) => isObject(value) && !Object.keys(value).length;\n\nvar isFileInput = (element) => element.type === 'file';\n\nvar isFunction = (value) => typeof value === 'function';\n\nvar isHTMLElement = (value) => {\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value ? value.ownerDocument : 0;\n    return (value instanceof\n        (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement));\n};\n\nvar isMultipleSelect = (element) => element.type === `select-multiple`;\n\nvar isRadioInput = (element) => element.type === 'radio';\n\nvar isRadioOrCheckbox = (ref) => isRadioInput(ref) || isCheckBoxInput(ref);\n\nvar live = (ref) => isHTMLElement(ref) && ref.isConnected;\n\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while (index < length) {\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for (const key in obj) {\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path)\n        ? path\n        : isKey(path)\n            ? [path]\n            : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 &&\n        ((isObject(childObject) && isEmptyObject(childObject)) ||\n            (Array.isArray(childObject) && isEmptyArray(childObject)))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\n\nvar objectHasFunction = (data) => {\n    for (const key in data) {\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\n\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            }\n            else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                if (isUndefined(formValues) ||\n                    isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key])\n                        ? markFieldsDirty(data[key], [])\n                        : { ...markFieldsDirty(data[key]) };\n                }\n                else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            }\n            else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\n\nconst defaultResult = {\n    value: false,\n    isValid: false,\n};\nconst validResult = { value: true, isValid: true };\nvar getCheckboxValue = (options) => {\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options\n                .filter((option) => option && option.checked && !option.disabled)\n                .map((option) => option.value);\n            return { value: values, isValid: !!values.length };\n        }\n        return options[0].checked && !options[0].disabled\n            ? // @ts-expect-error expected to work in the browser\n                options[0].attributes && !isUndefined(options[0].attributes.value)\n                    ? isUndefined(options[0].value) || options[0].value === ''\n                        ? validResult\n                        : { value: options[0].value, isValid: true }\n                    : validResult\n            : defaultResult;\n    }\n    return defaultResult;\n};\n\nvar getFieldValueAs = (value, { valueAsNumber, valueAsDate, setValueAs }) => isUndefined(value)\n    ? value\n    : valueAsNumber\n        ? value === ''\n            ? NaN\n            : value\n                ? +value\n                : value\n        : valueAsDate && isString(value)\n            ? new Date(value)\n            : setValueAs\n                ? setValueAs(value)\n                : value;\n\nconst defaultReturn = {\n    isValid: false,\n    value: null,\n};\nvar getRadioValue = (options) => Array.isArray(options)\n    ? options.reduce((previous, option) => option && option.checked && !option.disabled\n        ? {\n            isValid: true,\n            value: option.value,\n        }\n        : previous, defaultReturn)\n    : defaultReturn;\n\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [...ref.selectedOptions].map(({ value }) => value);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n    const fields = {};\n    for (const name of fieldsNames) {\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [...fieldsNames],\n        fields,\n        shouldUseNativeValidation,\n    };\n};\n\nvar isRegex = (value) => value instanceof RegExp;\n\nvar getRuleValue = (rule) => isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n        ? rule.source\n        : isObject(rule)\n            ? isRegex(rule.value)\n                ? rule.value.source\n                : rule.value\n            : rule;\n\nvar getValidationModes = (mode) => ({\n    isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n    isOnBlur: mode === VALIDATION_MODE.onBlur,\n    isOnChange: mode === VALIDATION_MODE.onChange,\n    isOnAll: mode === VALIDATION_MODE.all,\n    isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\nvar hasPromiseValidation = (fieldReference) => !!fieldReference &&\n    !!fieldReference.validate &&\n    !!((isFunction(fieldReference.validate) &&\n        fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n        (isObject(fieldReference.validate) &&\n            Object.values(fieldReference.validate).find((validateFunction) => validateFunction.constructor.name === ASYNC_FUNCTION)));\n\nvar hasValidation = (options) => options.mount &&\n    (options.required ||\n        options.min ||\n        options.max ||\n        options.maxLength ||\n        options.minLength ||\n        options.pattern ||\n        options.validate);\n\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent &&\n    (_names.watchAll ||\n        _names.watch.has(name) ||\n        [..._names.watch].some((watchName) => name.startsWith(watchName) &&\n            /^\\.\\w+/.test(name.slice(watchName.length))));\n\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n    for (const key of fieldsNames || Object.keys(fields)) {\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                }\n                else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                }\n                else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            }\n            else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\n\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name,\n        };\n    }\n    const names = name.split('.');\n    while (names.length) {\n        const fieldName = names.join('.');\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return { name };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError,\n            };\n        }\n        if (foundError && foundError.root && foundError.root.type) {\n            return {\n                name: `${fieldName}.root`,\n                error: foundError.root,\n            };\n        }\n        names.pop();\n    }\n    return {\n        name,\n    };\n}\n\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return (isEmptyObject(formState) ||\n        Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n        Object.keys(formState).find((key) => _proxyFormState[key] ===\n            (!isRoot || VALIDATION_MODE.all)));\n};\n\nvar shouldSubscribeByName = (name, signalName, exact) => !name ||\n    !signalName ||\n    name === signalName ||\n    convertToArrayPayload(name).some((currentName) => currentName &&\n        (exact\n            ? currentName === signalName\n            : currentName.startsWith(signalName) ||\n                signalName.startsWith(currentName)));\n\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n    if (mode.isOnAll) {\n        return false;\n    }\n    else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    }\n    else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    }\n    else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\n\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\n\nvar updateFieldArrayRootError = (errors, error, name) => {\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, 'root', error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\n\nvar isMessage = (value) => isString(value);\n\nfunction getValidateError(result, ref, type = 'validate') {\n    if (isMessage(result) ||\n        (Array.isArray(result) && result.every(isMessage)) ||\n        (isBoolean(result) && !result)) {\n        return {\n            type,\n            message: isMessage(result) ? result : '',\n            ref,\n        };\n    }\n}\n\nvar getValueAndMessage = (validationData) => isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n    };\n\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount, } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message) => {\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = ((valueAsNumber || isFileInput(ref)) &&\n        isUndefined(ref.value) &&\n        isUndefined(inputValue)) ||\n        (isHTMLElement(ref) && ref.value === '') ||\n        inputValue === '' ||\n        (Array.isArray(inputValue) && !inputValue.length);\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n        };\n    };\n    if (isFieldArray\n        ? !Array.isArray(inputValue) || !inputValue.length\n        : required &&\n            ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n                (isBoolean(inputValue) && !inputValue) ||\n                (isCheckBox && !getCheckboxValue(refs).isValid) ||\n                (isRadio && !getRadioValue(refs).isValid))) {\n        const { value, message } = isMessage(required)\n            ? { value: !!required, message: required }\n            : getValueAndMessage(required);\n        if (value) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber ||\n                (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        }\n        else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time) => new Date(new Date().toDateString() + ' ' + time);\n            const isTime = ref.type == 'time';\n            const isWeek = ref.type == 'week';\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime\n                    ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n                    : isWeek\n                        ? inputValue > maxOutput.value\n                        : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime\n                    ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n                    : isWeek\n                        ? inputValue < minOutput.value\n                        : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) &&\n        !isEmpty &&\n        (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) &&\n            inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) &&\n            inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message),\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        }\n        else if (isObject(validate)) {\n            let validationResult = {};\n            for (const key in validate) {\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message),\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult,\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\n\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true,\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props,\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false,\n    };\n    let _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values)\n        ? cloneObject(_options.defaultValues || _options.values) || {}\n        : {};\n    let _formValues = _options.shouldUnregister\n        ? {}\n        : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false,\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set(),\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState,\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject(),\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback) => (wait) => {\n        clearTimeout(timer);\n        timer = setTimeout(callback, wait);\n    };\n    const _setValid = async (shouldUpdateValid) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValid ||\n                _proxySubscribeFormState.isValid ||\n                shouldUpdateValid)) {\n            const isValid = _options.resolver\n                ? isEmptyObject((await _runSchema()).errors)\n                : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid,\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValidating ||\n                _proxyFormState.validatingFields ||\n                _proxySubscribeFormState.isValidating ||\n                _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name) => {\n                if (name) {\n                    isValidating\n                        ? set(_formState.validatingFields, name, isValidating)\n                        : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields),\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true) => {\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields ||\n                _proxySubscribeFormState.touchedFields) &&\n                shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid,\n            });\n        }\n        else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error) => {\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors,\n        });\n    };\n    const _setErrors = (errors) => {\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false,\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n            isUndefined(defaultValue) ||\n                (ref && ref.defaultChecked) ||\n                shouldSkipSetValueAs\n                ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f))\n                : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name,\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine\n                    ? unset(_formState.dirtyFields, name)\n                    : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField =\n                    shouldUpdateField ||\n                        ((_proxyFormState.dirtyFields ||\n                            _proxySubscribeFormState.dirtyFields) &&\n                            isPreviousDirty !== !isCurrentFieldPristine);\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField =\n                        shouldUpdateField ||\n                            ((_proxyFormState.touchedFields ||\n                                _proxySubscribeFormState.touchedFields) &&\n                                isPreviousFieldTouched !== isBlurEvent);\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState) => {\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n            isBoolean(isValid) &&\n            _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(() => updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        }\n        else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error\n                ? set(_formState.errors, name, error)\n                : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n            !isEmptyObject(fieldState) ||\n            shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n                errors: _formState.errors,\n                name,\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState,\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name) => {\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names) => {\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names) {\n                const error = get(errors, name);\n                error\n                    ? set(_formState.errors, name, error)\n                    : unset(_formState.errors, name);\n            }\n        }\n        else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true,\n    }) => {\n        for (const name in fields) {\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid &&\n                        (get(fieldError, _f.name)\n                            ? isFieldArrayRoot\n                                ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name)\n                                : set(_formState.errors, _f.name, fieldError[_f.name])\n                            : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) &&\n                    (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = () => {\n        for (const name of _names.unMount) {\n            const field = get(_fields, name);\n            field &&\n                (field._f.refs\n                    ? field._f.refs.every((ref) => !live(ref))\n                    : !live(field._f.ref)) &&\n                unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data) => !_options.disabled &&\n        (name && data && set(_formValues, name, data),\n            !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {\n        ...(_state.mount\n            ? _formValues\n            : isUndefined(defaultValue)\n                ? _defaultValues\n                : isString(names)\n                    ? { [names]: defaultValue }\n                    : defaultValue),\n    }, isGlobal, defaultValue);\n    const _getFieldArray = (name) => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        let fieldValue = value;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled &&\n                    set(_formValues, name, getFieldValueAs(value, fieldReference));\n                fieldValue =\n                    isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n                        ? ''\n                        : value;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [...fieldReference.ref.options].forEach((optionRef) => (optionRef.selected = fieldValue.includes(optionRef.value)));\n                }\n                else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef) => {\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data) => data === checkboxRef.value);\n                                }\n                                else {\n                                    checkboxRef.checked =\n                                        fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    }\n                    else {\n                        fieldReference.refs.forEach((radioRef) => (radioRef.checked = radioRef.value === fieldValue));\n                    }\n                }\n                else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = '';\n                }\n                else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues),\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) &&\n            updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value, options) => {\n        for (const fieldKey in value) {\n            if (!value.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value[fieldKey];\n            const fieldName = name + '.' + fieldKey;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) ||\n                isObject(fieldValue) ||\n                (field && !field._f)) &&\n                !isDateObject(fieldValue)\n                ? setValues(fieldName, fieldValue, options)\n                : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues),\n            });\n            if ((_proxyFormState.isDirty ||\n                _proxyFormState.dirtyFields ||\n                _proxySubscribeFormState.isDirty ||\n                _proxySubscribeFormState.dirtyFields) &&\n                options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue),\n                });\n            }\n        }\n        else {\n            field && !field._f && !isNullOrUndefined(cloneValue)\n                ? setValues(name, cloneValue, options)\n                : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({ ..._formState, name });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues),\n        });\n    };\n    const onChange = async (event) => {\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue) => {\n            isFieldValueUpdated =\n                Number.isNaN(fieldValue) ||\n                    (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n                    deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type\n                ? getFieldValue(field._f)\n                : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = (!hasValidation(field._f) &&\n                !_options.resolver &&\n                !get(_formState.errors, name) &&\n                !field._f.deps) ||\n                skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            }\n            else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent &&\n                _subjects.state.next({\n                    name,\n                    type: event.type,\n                    values: cloneObject(_formValues),\n                });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === 'onBlur') {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    }\n                    else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return (shouldRender &&\n                    _subjects.state.next({ name, ...(watched ? {} : fieldState) }));\n            }\n            !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            }\n            else {\n                _updateIsValidating([name], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    }\n                    else if (_proxyFormState.isValid ||\n                        _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps &&\n                    trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key) => {\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {}) => {\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name\n                ? !fieldNames.some((name) => get(errors, name))\n                : isValid;\n        }\n        else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName) => {\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? { [fieldName]: field } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        }\n        else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...(!isString(name) ||\n                ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n                    isValid !== _formState.isValid)\n                ? {}\n                : { name }),\n            ...(_options.resolver || !name ? { isValid } : {}),\n            errors: _formState.errors,\n        });\n        options.shouldFocus &&\n            !validationResult &&\n            iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames) => {\n        const values = {\n            ...(_state.mount ? _formValues : _defaultValues),\n        };\n        return isUndefined(fieldNames)\n            ? values\n            : isString(fieldNames)\n                ? get(values, fieldNames)\n                : fieldNames.map((name) => get(values, name));\n    };\n    const getFieldState = (name, formState) => ({\n        invalid: !!get((formState || _formState).errors, name),\n        isDirty: !!get((formState || _formState).dirtyFields, name),\n        error: get((formState || _formState).errors, name),\n        isValidating: !!get(_formState.validatingFields, name),\n        isTouched: !!get((formState || _formState).touchedFields, name),\n    });\n    const clearErrors = (name) => {\n        name &&\n            convertToArrayPayload(name).forEach((inputName) => unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {},\n        });\n    };\n    const setError = (name, error, options) => {\n        const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref,\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false,\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue) => isFunction(name)\n        ? _subjects.state.subscribe({\n            next: (payload) => 'values' in payload &&\n                name(_getWatch(undefined, defaultValue), payload),\n        })\n        : _getWatch(name, defaultValue, true);\n    const _subscribe = (props) => _subjects.state.subscribe({\n        next: (formState) => {\n            if (shouldSubscribeByName(props.name, formState.name, props.exact) &&\n                shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                props.callback({\n                    values: { ..._formValues },\n                    ..._formState,\n                    ...formState,\n                    defaultValues: _defaultValues,\n                });\n            }\n        },\n    }).unsubscribe;\n    const subscribe = (props) => {\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState,\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState,\n        });\n    };\n    const unregister = (name, options = {}) => {\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating &&\n                unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister &&\n                !options.keepDefaultValue &&\n                unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues),\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name, }) => {\n        if ((isBoolean(disabled) && _state.mount) ||\n            !!disabled ||\n            _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {}) => {\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...(field || {}),\n            _f: {\n                ...(field && field._f ? field._f : { ref: { name } }),\n                name,\n                mount: true,\n                ...options,\n            },\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled)\n                    ? options.disabled\n                    : _options.disabled,\n                name,\n            });\n        }\n        else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...(disabledIsDefined\n                ? { disabled: options.disabled || _options.disabled }\n                : {}),\n            ...(_options.progressive\n                ? {\n                    required: !!options.required,\n                    min: getRuleValue(options.min),\n                    max: getRuleValue(options.max),\n                    minLength: getRuleValue(options.minLength),\n                    maxLength: getRuleValue(options.maxLength),\n                    pattern: getRuleValue(options.pattern),\n                }\n                : {}),\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref) => {\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value)\n                        ? ref.querySelectorAll\n                            ? ref.querySelectorAll('input,select,textarea')[0] || ref\n                            : ref\n                        : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox\n                        ? refs.find((option) => option === fieldRef)\n                        : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...(radioOrCheckbox\n                                ? {\n                                    refs: [\n                                        ...refs.filter(live),\n                                        fieldRef,\n                                        ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                                    ],\n                                    ref: { type: fieldRef.type, name },\n                                }\n                                : { ref: fieldRef }),\n                        },\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                }\n                else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) &&\n                        !(isNameInFieldArray(_names.array, name) && _state.action) &&\n                        _names.unMount.add(name);\n                }\n            },\n        };\n    };\n    const _focusError = () => _options.shouldFocusError &&\n        iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled) => {\n        if (isBoolean(disabled)) {\n            _subjects.state.next({ disabled });\n            iterateFieldsByAction(_fields, (ref, name) => {\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef) => {\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid) => async (e) => {\n        let onValidError = undefined;\n        if (e) {\n            e.preventDefault && e.preventDefault();\n            e.persist &&\n                e.persist();\n        }\n        let fieldValues = cloneObject(_formValues);\n        _subjects.state.next({\n            isSubmitting: true,\n        });\n        if (_options.resolver) {\n            const { errors, values } = await _runSchema();\n            _formState.errors = errors;\n            fieldValues = cloneObject(values);\n        }\n        else {\n            await executeBuiltInValidation(_fields);\n        }\n        if (_names.disabled.size) {\n            for (const name of _names.disabled) {\n                unset(fieldValues, name);\n            }\n        }\n        unset(_formState.errors, 'root');\n        if (isEmptyObject(_formState.errors)) {\n            _subjects.state.next({\n                errors: {},\n            });\n            try {\n                await onValid(fieldValues, e);\n            }\n            catch (error) {\n                onValidError = error;\n            }\n        }\n        else {\n            if (onInvalid) {\n                await onInvalid({ ..._formState.errors }, e);\n            }\n            _focusError();\n            setTimeout(_focusError);\n        }\n        _subjects.state.next({\n            isSubmitted: true,\n            isSubmitting: false,\n            isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n            submitCount: _formState.submitCount + 1,\n            errors: _formState.errors,\n        });\n        if (onValidError) {\n            throw onValidError;\n        }\n    };\n    const resetField = (name, options = {}) => {\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            }\n            else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue\n                    ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n                    : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({ ..._formState });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {}) => {\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)) {\n                    get(_formState.dirtyFields, fieldName)\n                        ? set(values, fieldName, get(_formValues, fieldName))\n                        : setValue(fieldName, get(values, fieldName));\n                }\n            }\n            else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount) {\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs)\n                                ? field._f.refs[0]\n                                : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest('form');\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                if (keepStateOptions.keepFieldsRef) {\n                    for (const fieldName of _names.mount) {\n                        setValue(fieldName, get(values, fieldName));\n                    }\n                }\n                else {\n                    _fields = {};\n                }\n            }\n            _formValues = _options.shouldUnregister\n                ? keepStateOptions.keepDefaultValues\n                    ? cloneObject(_defaultValues)\n                    : {}\n                : cloneObject(values);\n            _subjects.array.next({\n                values: { ...values },\n            });\n            _subjects.state.next({\n                values: { ...values },\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: '',\n        };\n        _state.mount =\n            !_proxyFormState.isValid ||\n                !!keepStateOptions.keepIsValid ||\n                !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount\n                ? _formState.submitCount\n                : 0,\n            isDirty: isEmptyResetValues\n                ? false\n                : keepStateOptions.keepDirty\n                    ? _formState.isDirty\n                    : !!(keepStateOptions.keepDefaultValues &&\n                        !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted\n                ? _formState.isSubmitted\n                : false,\n            dirtyFields: isEmptyResetValues\n                ? {}\n                : keepStateOptions.keepDirtyValues\n                    ? keepStateOptions.keepDefaultValues && _formValues\n                        ? getDirtyFields(_defaultValues, _formValues)\n                        : _formState.dirtyFields\n                    : keepStateOptions.keepDefaultValues && formValues\n                        ? getDirtyFields(_defaultValues, formValues)\n                        : keepStateOptions.keepDirty\n                            ? _formState.dirtyFields\n                            : {},\n            touchedFields: keepStateOptions.keepTouched\n                ? _formState.touchedFields\n                : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n                ? _formState.isSubmitSuccessful\n                : false,\n            isSubmitting: false,\n        });\n    };\n    const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues)\n        ? formValues(_formValues)\n        : formValues, keepStateOptions);\n    const setFocus = (name, options = {}) => {\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs\n                ? fieldReference.refs[0]\n                : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect &&\n                    isFunction(fieldRef.select) &&\n                    fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState) => {\n        _formState = {\n            ..._formState,\n            ...updatedFormState,\n        };\n    };\n    const _resetDefaultValues = () => isFunction(_options.defaultValues) &&\n        _options.defaultValues().then((values) => {\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false,\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _focusError,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields() {\n                return _fields;\n            },\n            get _formValues() {\n                return _formValues;\n            },\n            get _state() {\n                return _state;\n            },\n            set _state(value) {\n                _state = value;\n            },\n            get _defaultValues() {\n                return _defaultValues;\n            },\n            get _names() {\n                return _names;\n            },\n            set _names(value) {\n                _names = value;\n            },\n            get _formState() {\n                return _formState;\n            },\n            get _options() {\n                return _options;\n            },\n            set _options(value) {\n                _options = {\n                    ..._options,\n                    ...value,\n                };\n            },\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState,\n    };\n    return {\n        ...methods,\n        formControl: methods,\n    };\n}\n\nvar generateId = () => {\n    if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n        return crypto.randomUUID();\n    }\n    const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n    });\n};\n\nvar getFocusFieldName = (name, index, options = {}) => options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n        `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n\nvar appendAt = (data, value) => [\n    ...data,\n    ...convertToArrayPayload(value),\n];\n\nvar fillEmptyArray = (value) => Array.isArray(value) ? value.map(() => undefined) : undefined;\n\nfunction insert(data, index, value) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value),\n        ...data.slice(index),\n    ];\n}\n\nvar moveArrayAt = (data, from, to) => {\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\n\nvar prependAt = (data, value) => [\n    ...convertToArrayPayload(value),\n    ...convertToArrayPayload(data),\n];\n\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [...data];\n    for (const index of indexes) {\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index)\n    ? []\n    : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\n\nvar swapArrayAt = (data, indexA, indexB) => {\n    [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n\nvar updateAt = (fieldValues, index, value) => {\n    fieldValues[index] = value;\n    return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = 'id', shouldUnregister, rules, } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => rules &&\n        control.register(name, rules), [control, rules, name]);\n    useIsomorphicLayoutEffect(() => control._subjects.array.subscribe({\n        next: ({ values, name: fieldArrayName, }) => {\n            if (fieldArrayName === name || !fieldArrayName) {\n                const fieldValues = get(values, name);\n                if (Array.isArray(fieldValues)) {\n                    setFields(fieldValues);\n                    ids.current = fieldValues.map(generateId);\n                }\n            }\n        },\n    }).unsubscribe, [control, name]);\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues) => {\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [control, name]);\n    const append = (value, options) => {\n        const appendValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const prepend = (value, options) => {\n        const prependValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const remove = (index) => {\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) &&\n            set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index,\n        });\n    };\n    const insert$1 = (index, value, options) => {\n        const insertValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value),\n        });\n    };\n    const swap = (indexA, indexB) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB,\n        }, false);\n    };\n    const move = (from, to) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to,\n        }, false);\n    };\n    const update = (index, value) => {\n        const updateValue = cloneObject(value);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue,\n        }, true, false);\n    };\n    const replace = (value) => {\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([...updatedFieldArrayValues]);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, [...updatedFieldArrayValues], (data) => data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        control._state.action = false;\n        isWatched(name, control._names) &&\n            control._subjects.state.next({\n                ...control._formState,\n            });\n        if (_actioned.current &&\n            (!getValidationModes(control._options.mode).isOnSubmit ||\n                control._formState.isSubmitted) &&\n            !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([name]).then((result) => {\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError\n                        ? (!error && existingError.type) ||\n                            (error &&\n                                (existingError.type !== error.type ||\n                                    existingError.message !== error.message))\n                        : error && error.type) {\n                        error\n                            ? set(control._formState.errors, name, error)\n                            : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors,\n                        });\n                    }\n                });\n            }\n            else {\n                const field = get(control._fields, name);\n                if (field &&\n                    field._f &&\n                    !(getValidationModes(control._options.reValidateMode).isOnSubmit &&\n                        getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error) => !isEmptyObject(error) &&\n                        control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name),\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues),\n        });\n        control._names.focus &&\n            iterateFieldsByAction(control._fields, (ref, key) => {\n                if (control._names.focus &&\n                    key.startsWith(control._names.focus) &&\n                    ref.focus) {\n                    ref.focus();\n                    return 1;\n                }\n                return;\n            });\n        control._names.focus = '';\n        control._setValid();\n        _actioned.current = false;\n    }, [fields, name, control]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return () => {\n            const updateMounted = (name, value) => {\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, keyName, shouldUnregister]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [updateValues, name, control]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [updateValues, name, control]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [updateValues, name, control]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [updateValues, name, control]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [updateValues, name, control]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [updateValues, name, control]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [updateValues, name, control]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [updateValues, name, control]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => fields.map((field, index) => ({\n            ...field,\n            [keyName]: ids.current[index] || generateId(),\n        })), [fields, keyName]),\n    };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues)\n            ? undefined\n            : props.defaultValues,\n    });\n    if (!_formControl.current) {\n        if (props.formControl) {\n            _formControl.current = {\n                ...props.formControl,\n                formState,\n            };\n            if (props.defaultValues && !isFunction(props.defaultValues)) {\n                props.formControl.reset(props.defaultValues, props.resetOptions);\n            }\n        }\n        else {\n            const { formControl, ...rest } = createFormControl(props);\n            _formControl.current = {\n                ...rest,\n                formState,\n            };\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(() => {\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: () => updateFormState({ ...control._formState }),\n            reRenderRoot: true,\n        });\n        updateFormState((data) => ({\n            ...data,\n            isReady: true,\n        }));\n        control._formState.isReady = true;\n        return sub;\n    }, [control]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n    }, [control, props.mode, props.reValidateMode]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (props.errors) {\n            control._setErrors(props.errors);\n            control._focusError();\n        }\n    }, [control, props.errors]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        props.shouldUnregister &&\n            control._subjects.state.next({\n                values: control._getWatch(),\n            });\n    }, [control, props.shouldUnregister]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty,\n                });\n            }\n        }\n    }, [control, formState.isDirty]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, {\n                keepFieldsRef: true,\n                ...control._options.resetOptions,\n            });\n            _values.current = props.values;\n            updateFormState((state) => ({ ...state }));\n        }\n        else {\n            control._resetDefaultValues();\n        }\n    }, [control, props.values]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({ ...control._formState });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n\n\n//# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;