{"version": 3, "sources": ["../../../src/export/routes/app-page.ts"], "names": ["exportAppPage", "ExportedAppPageFiles", "req", "res", "page", "path", "pathname", "query", "renderOpts", "htmlFilepath", "debugOutput", "isDynamicError", "fileWriter", "isDefaultNotFound", "result", "lazyRenderAppPage", "html", "toUnchunkedString", "metadata", "flightData", "revalidate", "postponed", "fetchTags", "experimental", "ppr", "Error", "staticBailoutInfo", "description", "logDynamicUsageWarning", "stack", "replace", "RSC_PREFETCH_SUFFIX", "RSC_SUFFIX", "headers", "Object", "assign", "getHeaders", "NEXT_CACHE_TAGS_HEADER", "isParallelRoute", "test", "isNonSuccessfulStatusCode", "statusCode", "status", "undefined", "meta", "NEXT_META_SUFFIX", "JSON", "stringify", "hasNextSupport", "hasEmptyPrelude", "Boolean", "hasPostponed", "err", "isDynamicUsageError", "missingSuspenseWithCSRBailout", "isBailoutToCSRError", "dynamicUsageDescription", "dynamicUsageStack", "store", "errMessage", "message", "substring", "indexOf", "console", "warn"], "mappings": ";;;;;;;;;;;;;;;;;;IA6BsBA,aAAa;eAAbA;;;qCAnBc;2BAM7B;wBACwB;8BACG;8BACE;;UAElBC;;;;;;GAAAA,yBAAAA;AAQX,eAAeD,cACpBE,GAAkB,EAClBC,GAAmB,EACnBC,IAAY,EACZC,IAAY,EACZC,QAAgB,EAChBC,KAAyB,EACzBC,UAAsB,EACtBC,YAAoB,EACpBC,WAAoB,EACpBC,cAAuB,EACvBC,UAAsB;IAEtB,IAAIC,oBAAoB;IACxB,6EAA6E;IAC7E,qJAAqJ;IACrJ,IAAIT,SAAS,oBAAoB;QAC/BS,oBAAoB;QACpBP,WAAW;IACb;IAEA,IAAI;QACF,MAAMQ,SAAS,MAAMC,IAAAA,+BAAiB,EACpCb,KACAC,KACAG,UACAC,OACAC;QAGF,MAAMQ,OAAOF,OAAOG,iBAAiB;QAErC,MAAM,EAAEC,QAAQ,EAAE,GAAGJ;QACrB,MAAM,EAAEK,UAAU,EAAEC,aAAa,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE,GAAGJ;QAEjE,uDAAuD;QACvD,IAAIG,aAAa,CAACb,WAAWe,YAAY,CAACC,GAAG,EAAE;YAC7C,MAAM,IAAIC,MAAM;QAClB;QAEA,IAAIL,eAAe,GAAG;YACpB,IAAIT,gBAAgB;gBAClB,MAAM,IAAIc,MACR,CAAC,+DAA+D,EAAEpB,KAAK,CAAC,CAAC;YAE7E;YACA,MAAM,EAAEqB,oBAAoB,CAAC,CAAC,EAAE,GAAGR;YAEnC,IAAIE,eAAe,KAAKV,gBAAegB,qCAAAA,kBAAmBC,WAAW,GAAE;gBACrEC,uBAAuB;oBACrBvB;oBACAsB,aAAaD,kBAAkBC,WAAW;oBAC1CE,OAAOH,kBAAkBG,KAAK;gBAChC;YACF;YAEA,OAAO;gBAAET,YAAY;YAAE;QACzB,OAGK,IAAI,CAACD,YAAY;YACpB,MAAM,IAAIM,MAAM,CAAC,uCAAuC,EAAEpB,KAAK,CAAC;QAClE,OAIK,IAAIG,WAAWe,YAAY,CAACC,GAAG,EAAE;YACpC,oEAAoE;YACpE,WAAW;YACX,MAAMZ,8BAEJH,aAAaqB,OAAO,CAAC,WAAWC,8BAAmB,GACnDZ;QAEJ,OAAO;YACL,kEAAkE;YAClE,MAAMP,qBAEJH,aAAaqB,OAAO,CAAC,WAAWE,qBAAU,GAC1Cb;QAEJ;QAEA,MAAMc,UAA+B;YAAE,GAAGf,SAASe,OAAO;QAAC;QAE3D,2EAA2E;QAC3E,6BAA6B;QAC7B,IAAIzB,WAAWe,YAAY,CAACC,GAAG,EAAE;YAC/BU,OAAOC,MAAM,CAACF,SAAS9B,IAAIiC,UAAU;QACvC;QAEA,IAAId,WAAW;YACbW,OAAO,CAACI,iCAAsB,CAAC,GAAGf;QACpC;QAEA,iCAAiC;QACjC,MAAMV,mBAEJH,cACAO,QAAQ,IACR;QAGF,MAAMsB,kBAAkB,SAASC,IAAI,CAACnC;QACtC,MAAMoC,4BAA4BrC,IAAIsC,UAAU,GAAG;QACnD,0EAA0E;QAC1E,kEAAkE;QAClE,YAAY;QACZ,IAAIC,SAA6BlC,WAAWe,YAAY,CAACC,GAAG,GACxDrB,IAAIsC,UAAU,GACdE;QAEJ,IAAI9B,mBAAmB;YACrB,2DAA2D;YAC3D6B,SAAS;QACX,OAAO,IAAIF,6BAA6B,CAACF,iBAAiB;YACxD,8DAA8D;YAC9DI,SAASvC,IAAIsC,UAAU;QACzB;QAEA,0CAA0C;QAC1C,MAAMG,OAAsB;YAC1BF;YACAT;YACAZ;QACF;QAEA,MAAMT,mBAEJH,aAAaqB,OAAO,CAAC,WAAWe,2BAAgB,GAChDC,KAAKC,SAAS,CAACH,MAAM,MAAM;QAG7B,OAAO;YACL,iEAAiE;YACjE1B,UAAU8B,sBAAc,GAAGJ,OAAOD;YAClCM,iBAAiBC,QAAQ7B,cAAcL,SAAS;YAChDmC,cAAcD,QAAQ7B;YACtBD;QACF;IACF,EAAE,OAAOgC,KAAK;QACZ,IAAI,CAACC,IAAAA,wCAAmB,EAACD,MAAM;YAC7B,MAAMA;QACR;QAEA,0EAA0E;QAC1E,8BAA8B;QAC9B,IACE5C,WAAWe,YAAY,CAAC+B,6BAA6B,IACrDC,IAAAA,iCAAmB,EAACH,MACpB;YACA,MAAMA;QACR;QAEA,IAAI1C,aAAa;YACf,MAAM,EAAE8C,uBAAuB,EAAEC,iBAAiB,EAAE,GAAG,AAACjD,WACrDkD,KAAK;YAER9B,uBAAuB;gBACrBvB;gBACAsB,aAAa6B;gBACb3B,OAAO4B;YACT;QACF;QAEA,OAAO;YAAErC,YAAY;QAAE;IACzB;AACF;AAEA,SAASQ,uBAAuB,EAC9BvB,IAAI,EACJsB,WAAW,EACXE,KAAK,EAKN;IACC,MAAM8B,aAAa,IAAIlC,MACrB,CAAC,iDAAiD,EAAEpB,KAAK,UAAU,EAAEsB,YAAY,CAAC;IAGpF,IAAIE,OAAO;QACT8B,WAAW9B,KAAK,GAAG8B,WAAWC,OAAO,GAAG/B,MAAMgC,SAAS,CAAChC,MAAMiC,OAAO,CAAC;IACxE;IAEAC,QAAQC,IAAI,CAACL;AACf"}