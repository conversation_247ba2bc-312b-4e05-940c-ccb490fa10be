// User and Authentication Types
export interface User {
  id: string
  email: string
  name: string
  role: UserRole
  avatar?: string
  createdAt: Date
  updatedAt: Date
}

export type UserRole = 'admin' | 'teacher' | 'staff'

export interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
}

// Login Form Types
export interface LoginFormData {
  username: string
  password: string
  rememberMe: boolean
}

export interface LoginResponse {
  success: boolean
  user?: User
  token?: string
  message?: string
  error?: string
}

// Student Types
export interface Student {
  id: string
  studentId: string
  firstName: string
  lastName: string
  middleName?: string
  email?: string
  phone?: string
  dateOfBirth: Date
  address: string
  course: string
  yearLevel: number
  section: string
  status: StudentStatus
  qrCode: string
  avatar?: string
  guardianName?: string
  guardianPhone?: string
  enrollmentDate: Date
  createdAt: Date
  updatedAt: Date
}

export type StudentStatus = 'active' | 'inactive' | 'graduated' | 'dropped'

export interface StudentFormData {
  studentId: string
  firstName: string
  lastName: string
  middleName?: string
  email?: string
  phone?: string
  dateOfBirth: string
  address: string
  course: string
  yearLevel: number
  section: string
  guardianName?: string
  guardianPhone?: string
}

// Attendance Types
export interface Attendance {
  id: string
  studentId: string
  student?: Student
  date: Date
  timeIn?: Date
  timeOut?: Date
  status: AttendanceStatus
  location?: string
  notes?: string
  scannedBy: string
  scannedByUser?: User
  createdAt: Date
  updatedAt: Date
}

export type AttendanceStatus = 'present' | 'absent' | 'late' | 'excused'

export interface AttendanceRecord {
  date: string
  status: AttendanceStatus
  timeIn?: string
  timeOut?: string
  notes?: string
}

export interface AttendanceSummary {
  totalDays: number
  presentDays: number
  absentDays: number
  lateDays: number
  excusedDays: number
  attendanceRate: number
}

// Course and Academic Types
export interface Course {
  id: string
  code: string
  name: string
  description?: string
  department: string
  credits: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Section {
  id: string
  name: string
  course: string
  yearLevel: number
  capacity: number
  currentEnrollment: number
  adviser?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Analytics and Reports Types
export interface DashboardStats {
  totalStudents: number
  presentToday: number
  absentToday: number
  lateToday: number
  attendanceRate: number
  totalCourses: number
  activeSections: number
}

export interface AttendanceAnalytics {
  dailyAttendance: DailyAttendanceData[]
  weeklyTrends: WeeklyTrendData[]
  monthlyStats: MonthlyStatsData[]
  courseWiseAttendance: CourseAttendanceData[]
}

export interface DailyAttendanceData {
  date: string
  present: number
  absent: number
  late: number
  total: number
}

export interface WeeklyTrendData {
  week: string
  attendanceRate: number
  totalStudents: number
}

export interface MonthlyStatsData {
  month: string
  year: number
  averageAttendance: number
  totalClasses: number
}

export interface CourseAttendanceData {
  course: string
  attendanceRate: number
  totalStudents: number
  presentStudents: number
}

// QR Code and Scanning Types
export interface QRScanResult {
  studentId: string
  timestamp: Date
  location?: string
  isValid: boolean
  error?: string
}

export interface ScanSession {
  id: string
  startTime: Date
  endTime?: Date
  location: string
  scannedBy: string
  totalScans: number
  successfulScans: number
  failedScans: number
  isActive: boolean
}

// Settings and Configuration Types
export interface AppSettings {
  schoolName: string
  schoolAddress: string
  schoolLogo?: string
  academicYear: string
  semester: string
  attendanceGracePeriod: number // minutes
  autoLogoutTime: number // minutes
  enableNotifications: boolean
  defaultTheme: 'light' | 'dark' | 'system'
  timeZone: string
}

export interface NotificationSettings {
  emailNotifications: boolean
  pushNotifications: boolean
  attendanceAlerts: boolean
  weeklyReports: boolean
  monthlyReports: boolean
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Form and UI Types
export interface FormState {
  isLoading: boolean
  errors: Record<string, string>
  isDirty: boolean
}

export interface TableColumn<T> {
  key: keyof T
  label: string
  sortable?: boolean
  render?: (value: any, row: T) => React.ReactNode
}

export interface FilterOptions {
  course?: string
  section?: string
  yearLevel?: number
  status?: StudentStatus | AttendanceStatus
  dateRange?: {
    start: Date
    end: Date
  }
}

// Navigation and Layout Types
export interface NavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: string | number
  children?: NavItem[]
}

export interface BreadcrumbItem {
  title: string
  href?: string
}

// Error and Loading Types
export interface ErrorState {
  message: string
  code?: string
  details?: any
}

export interface LoadingState {
  isLoading: boolean
  message?: string
}
