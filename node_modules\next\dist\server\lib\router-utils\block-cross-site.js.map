{"version": 3, "sources": ["../../../../src/server/lib/router-utils/block-cross-site.ts"], "names": ["blockCrossSite", "warnOrBlockRequest", "res", "origin", "mode", "originString", "warnOnce", "statusCode", "end", "isInternalDevEndpoint", "req", "url", "isMiddlewareRequest", "includes", "isInternalAsset", "isIgnoredRequest", "err", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hostname", "<PERSON><PERSON><PERSON><PERSON>", "push", "headers", "undefined", "rawOrigin", "parsed<PERSON><PERSON>in", "parseUrl", "originLowerCase", "toLowerCase", "isCsrfOriginAllowed"], "mappings": ";;;;+BAoDaA;;;eAAAA;;;qBAlDY;qBACA;gCACW;AAEpC,SAASC,mBACPC,GAA4B,EAC5BC,MAA0B,EAC1BC,IAAsB;IAEtB,MAAMC,eAAeF,SAAS,CAAC,KAAK,EAAEA,OAAO,CAAC,GAAG;IACjD,IAAIC,SAAS,QAAQ;QACnBE,IAAAA,aAAQ,EACN,CAAC,8BAA8B,EAAED,aAAa,kPAAkP,CAAC;QAGnS,OAAO;IACT;IAEAC,IAAAA,aAAQ,EACN,CAAC,6BAA6B,EAAED,aAAa,gLAAgL,CAAC;IAGhO,IAAI,gBAAgBH,KAAK;QACvBA,IAAIK,UAAU,GAAG;IACnB;IAEAL,IAAIM,GAAG,CAAC;IAER,OAAO;AACT;AAEA,SAASC,sBAAsBC,GAAoB;IACjD,IAAI,CAACA,IAAIC,GAAG,EAAE,OAAO;IAErB,IAAI;QACF,0DAA0D;QAC1D,MAAMC,sBAAsBF,IAAIC,GAAG,CAACE,QAAQ,CAAC;QAC7C,MAAMC,kBAAkBJ,IAAIC,GAAG,CAACE,QAAQ,CAAC;QACzC,qFAAqF;QACrF,eAAe;QACf,MAAME,mBACJL,IAAIC,GAAG,CAACE,QAAQ,CAAC,mBACjBH,IAAIC,GAAG,CAACE,QAAQ,CAAC;QAEnB,OAAO,CAACE,oBAAqBD,CAAAA,mBAAmBF,mBAAkB;IACpE,EAAE,OAAOI,KAAK;QACZ,OAAO;IACT;AACF;AAEO,MAAMhB,iBAAiB,CAC5BU,KACAR,KACAe,mBACAC;IAEA,0FAA0F;IAC1F,0DAA0D;IAC1D,MAAMd,OAAO,OAAOa,sBAAsB,cAAc,SAAS;IAEjE,MAAME,iBAAiB;QACrB;QACA;WACIF,qBAAqB,EAAE;KAC5B;IACD,IAAIC,UAAU;QACZC,eAAeC,IAAI,CAACF;IACtB;IAEA,wCAAwC;IACxC,IAAI,CAACT,sBAAsBC,MAAM;QAC/B,OAAO;IACT;IACA,4DAA4D;IAC5D,iBAAiB;IACjB,IACEA,IAAIW,OAAO,CAAC,iBAAiB,KAAK,aAClCX,IAAIW,OAAO,CAAC,iBAAiB,KAAK,cAClC;QACA,OAAOpB,mBAAmBC,KAAKoB,WAAWlB;IAC5C;IAEA,gDAAgD;IAChD,MAAMmB,YAAYb,IAAIW,OAAO,CAAC,SAAS;IAEvC,IAAIE,WAAW;QACb,MAAMC,eAAeC,IAAAA,aAAQ,EAACF;QAE9B,IAAIC,cAAc;YAChB,MAAME,kBAAkBF,aAAaN,QAAQ,CAACS,WAAW;YAEzD,IAAI,CAACC,IAAAA,mCAAmB,EAACF,iBAAiBP,iBAAiB;gBACzD,OAAOlB,mBAAmBC,KAAKwB,iBAAiBtB;YAClD;QACF;IACF;IAEA,OAAO;AACT"}