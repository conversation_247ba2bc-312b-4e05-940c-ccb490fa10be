{"version": 3, "sources": ["../../../src/shared/lib/app-dynamic.tsx"], "names": ["dynamic", "dynamicOptions", "options", "mergedOptions", "loadableOptions", "loading", "error", "isLoading", "past<PERSON>elay", "process", "env", "NODE_ENV", "p", "message", "br", "stack", "loader", "Loadable", "modules", "loadableGenerated"], "mappings": ";;;;+BAiCA;;;eAAwBA;;;;;gEAjCN;mEACG;AAgCN,SAASA,QACtBC,cAA6C,EAC7CC,OAA2B;QAmChBC;IAjCX,IAAIC,kBAAsC;QACxC,wDAAwD;QACxDC,SAAS;gBAAC,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;YACvC,IAAI,CAACA,WAAW,OAAO;YACvB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAIJ,WAAW;oBACb,OAAO;gBACT;gBACA,IAAID,OAAO;oBACT,qBACE,sBAACM;;4BACEN,MAAMO,OAAO;0CACd,qBAACC;4BACAR,MAAMS,KAAK;;;gBAGlB;YACF;YACA,OAAO;QACT;IACF;IAEA,IAAI,OAAOd,mBAAmB,YAAY;QACxCG,gBAAgBY,MAAM,GAAGf;IAC3B;IAEA,MAAME,gBAAgB;QACpB,GAAGC,eAAe;QAClB,GAAGF,OAAO;IACZ;IAEA,OAAOe,IAAAA,iBAAQ,EAAC;QACd,GAAGd,aAAa;QAChBe,OAAO,GAAEf,mCAAAA,cAAcgB,iBAAiB,qBAA/BhB,iCAAiCe,OAAO;IACnD;AACF"}