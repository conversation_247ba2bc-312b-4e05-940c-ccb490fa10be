{"version": 3, "sources": ["../../../../src/shared/lib/router/action-queue.ts"], "names": ["ActionQueueContext", "createMutableActionQueue", "React", "createContext", "runRemainingActions", "actionQueue", "setState", "pending", "next", "runAction", "action", "needsRefresh", "dispatch", "type", "ACTION_REFRESH", "origin", "window", "location", "prevState", "state", "Error", "payload", "actionResult", "handleResult", "nextState", "discarded", "devToolsInstance", "send", "resolve", "isThenable", "then", "err", "reject", "dispatchAction", "resolvers", "ACTION_RESTORE", "deferred<PERSON><PERSON><PERSON>", "Promise", "startTransition", "newAction", "last", "ACTION_NAVIGATE", "ACTION_SERVER_ACTION", "result", "reducer"], "mappings": ";;;;;;;;;;;;;;;IAkCaA,kBAAkB;eAAlBA;;IA2JGC,wBAAwB;eAAxBA;;;;oCApLT;+BAEiB;iEACe;AAsBhC,MAAMD,qBACXE,cAAK,CAACC,aAAa,CAA8B;AAEnD,SAASC,oBACPC,WAAiC,EACjCC,QAA8B;IAE9B,IAAID,YAAYE,OAAO,KAAK,MAAM;QAChCF,YAAYE,OAAO,GAAGF,YAAYE,OAAO,CAACC,IAAI;QAC9C,IAAIH,YAAYE,OAAO,KAAK,MAAM;YAChC,mEAAmE;YACnEE,UAAU;gBACRJ;gBACAK,QAAQL,YAAYE,OAAO;gBAC3BD;YACF;QACF,OAAO;YACL,4DAA4D;YAC5D,IAAID,YAAYM,YAAY,EAAE;gBAC5BN,YAAYM,YAAY,GAAG;gBAC3BN,YAAYO,QAAQ,CAClB;oBACEC,MAAMC,kCAAc;oBACpBC,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC,GACAT;YAEJ;QACF;IACF;AACF;AAEA,eAAeG,UAAU,KAQxB;IARwB,IAAA,EACvBJ,WAAW,EACXK,MAAM,EACNJ,QAAQ,EAKT,GARwB;IASvB,MAAMY,YAAYb,YAAYc,KAAK;IACnC,IAAI,CAACD,WAAW;QACd,sFAAsF;QACtF,MAAM,IAAIE,MAAM;IAClB;IAEAf,YAAYE,OAAO,GAAGG;IAEtB,MAAMW,UAAUX,OAAOW,OAAO;IAC9B,MAAMC,eAAejB,YAAYK,MAAM,CAACQ,WAAWG;IAEnD,SAASE,aAAaC,SAAyB;QAC7C,kEAAkE;QAClE,IAAId,OAAOe,SAAS,EAAE;YACpB;QACF;QAEApB,YAAYc,KAAK,GAAGK;QAEpB,IAAInB,YAAYqB,gBAAgB,EAAE;YAChCrB,YAAYqB,gBAAgB,CAACC,IAAI,CAACN,SAASG;QAC7C;QAEApB,oBAAoBC,aAAaC;QACjCI,OAAOkB,OAAO,CAACJ;IACjB;IAEA,8DAA8D;IAC9D,IAAIK,IAAAA,8BAAU,EAACP,eAAe;QAC5BA,aAAaQ,IAAI,CAACP,cAAc,CAACQ;YAC/B3B,oBAAoBC,aAAaC;YACjCI,OAAOsB,MAAM,CAACD;QAChB;IACF,OAAO;QACLR,aAAaD;IACf;AACF;AAEA,SAASW,eACP5B,WAAiC,EACjCgB,OAAuB,EACvBf,QAA8B;IAE9B,IAAI4B,YAGA;QAAEN,SAAStB;QAAU0B,QAAQ,KAAO;IAAE;IAE1C,mEAAmE;IACnE,wFAAwF;IACxF,2DAA2D;IAC3D,oDAAoD;IACpD,IAAIX,QAAQR,IAAI,KAAKsB,kCAAc,EAAE;QACnC,6DAA6D;QAC7D,MAAMC,kBAAkB,IAAIC,QAAwB,CAACT,SAASI;YAC5DE,YAAY;gBAAEN;gBAASI;YAAO;QAChC;QAEAM,IAAAA,sBAAe,EAAC;YACd,oGAAoG;YACpG,iEAAiE;YACjEhC,SAAS8B;QACX;IACF;IAEA,MAAMG,YAA6B;QACjClB;QACAb,MAAM;QACNoB,SAASM,UAAUN,OAAO;QAC1BI,QAAQE,UAAUF,MAAM;IAC1B;IAEA,8BAA8B;IAC9B,IAAI3B,YAAYE,OAAO,KAAK,MAAM;QAChC,iEAAiE;QACjE,4CAA4C;QAC5CF,YAAYmC,IAAI,GAAGD;QAEnB9B,UAAU;YACRJ;YACAK,QAAQ6B;YACRjC;QACF;IACF,OAAO,IACLe,QAAQR,IAAI,KAAK4B,mCAAe,IAChCpB,QAAQR,IAAI,KAAKsB,kCAAc,EAC/B;QACA,+EAA+E;QAC/E,oHAAoH;QACpH9B,YAAYE,OAAO,CAACkB,SAAS,GAAG;QAEhC,4CAA4C;QAC5CpB,YAAYmC,IAAI,GAAGD;QAEnB,2GAA2G;QAC3G,IAAIlC,YAAYE,OAAO,CAACc,OAAO,CAACR,IAAI,KAAK6B,wCAAoB,EAAE;YAC7DrC,YAAYM,YAAY,GAAG;QAC7B;QAEAF,UAAU;YACRJ;YACAK,QAAQ6B;YACRjC;QACF;IACF,OAAO;QACL,oEAAoE;QACpE,+EAA+E;QAC/E,IAAID,YAAYmC,IAAI,KAAK,MAAM;YAC7BnC,YAAYmC,IAAI,CAAChC,IAAI,GAAG+B;QAC1B;QACAlC,YAAYmC,IAAI,GAAGD;IACrB;AACF;AAEO,SAAStC;IACd,MAAMI,cAAoC;QACxCc,OAAO;QACPP,UAAU,CAACS,SAAyBf,WAClC2B,eAAe5B,aAAagB,SAASf;QACvCI,QAAQ,OAAOS,OAAuBT;YACpC,IAAIS,UAAU,MAAM;gBAClB,MAAM,IAAIC,MAAM;YAClB;YACA,MAAMuB,SAASC,IAAAA,sBAAO,EAACzB,OAAOT;YAC9B,OAAOiC;QACT;QACApC,SAAS;QACTiC,MAAM;IACR;IAEA,OAAOnC;AACT"}