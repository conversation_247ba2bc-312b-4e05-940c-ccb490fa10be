"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Eye, EyeOff, LogIn, GraduationCap } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { ThemeToggle } from "@/components/theme-toggle"
import { LoginFormData } from "@/types"

// Form validation schema
const loginSchema = z.object({
  username: z.string().min(1, "Username or Employee ID is required"),
  password: z.string().min(1, "Password is required"),
  rememberMe: z.boolean().default(false),
})

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
      rememberMe: false,
    },
  })

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true)
    setError(null)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Simulate login validation
      if (data.username === "admin" && data.password === "password") {
        console.log("Login successful:", data)
        // Redirect to dashboard or handle successful login
        window.location.href = "/"
      } else {
        setError("Invalid username or password. Please try again.")
      }
    } catch (error) {
      console.error("Login error:", error)
      setError("An error occurred during login. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-education-blue-50 via-white to-education-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex flex-col">
      {/* Theme Toggle */}
      <div className="absolute top-4 right-4 z-10">
        <ThemeToggle />
      </div>

      {/* Hero Section */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-6xl grid lg:grid-cols-2 gap-8 items-center">
          {/* Left Side - Hero Content */}
          <div className="hidden lg:flex flex-col items-center justify-center space-y-6 text-center animate-in fade-in-50 slide-in-from-left-5 duration-700">
            <div className="w-32 h-32 bg-education-blue-100 dark:bg-education-blue-900/20 rounded-full flex items-center justify-center">
              <GraduationCap className="w-16 h-16 text-education-blue-600 dark:text-education-blue-400" />
            </div>
            
            <div className="space-y-4">
              <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
                Welcome to QRSAMS
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                QR-Code Based Student Attendance and Monitoring System
              </p>
              
              <div className="space-y-2 text-gray-700 dark:text-gray-400">
                <h2 className="text-2xl font-semibold text-education-blue-700 dark:text-education-blue-300">
                  Tanauan School of Arts and Trade
                </h2>
                <p className="text-lg">
                  Brgy. Cabuyan, Tanauan, Leyte
                </p>
              </div>
            </div>

            {/* School Image Placeholder */}
            <div className="w-full max-w-md h-48 bg-gradient-to-r from-education-blue-100 to-education-green-100 dark:from-education-blue-900/20 dark:to-education-green-900/20 rounded-lg flex items-center justify-center border-2 border-dashed border-education-blue-300 dark:border-education-blue-600">
              <div className="text-center space-y-2">
                <GraduationCap className="w-12 h-12 text-education-blue-500 mx-auto" />
                <p className="text-sm text-gray-500 dark:text-gray-400">School Image Placeholder</p>
              </div>
            </div>
          </div>

          {/* Right Side - Login Form */}
          <div className="w-full max-w-md mx-auto">
            <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm transform transition-all duration-300 hover:shadow-3xl animate-in fade-in-50 slide-in-from-bottom-5">
              <CardHeader className="space-y-1 text-center">
                <div className="flex justify-center mb-4">
                  <div className="w-16 h-16 bg-education-blue-100 dark:bg-education-blue-900/20 rounded-full flex items-center justify-center">
                    <LogIn className="w-8 h-8 text-education-blue-600 dark:text-education-blue-400" />
                  </div>
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                  Sign In
                </CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-400">
                  Enter your credentials to access the system
                </CardDescription>

                {/* Demo Credentials Hint */}
                <div className="mt-4 p-3 bg-education-blue-50 dark:bg-education-blue-900/20 border border-education-blue-200 dark:border-education-blue-800 rounded-md">
                  <p className="text-xs text-education-blue-700 dark:text-education-blue-300 font-medium mb-1">
                    Demo Credentials:
                  </p>
                  <p className="text-xs text-education-blue-600 dark:text-education-blue-400">
                    Username: <span className="font-mono">admin</span> | Password: <span className="font-mono">password</span>
                  </p>
                </div>
              </CardHeader>
              
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    {/* Error Message */}
                    {error && (
                      <div className="p-3 text-sm text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400 border border-red-200 dark:border-red-800 rounded-md">
                        {error}
                      </div>
                    )}
                    {/* Username Field */}
                    <FormField
                      control={form.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Username / Employee ID</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter your username or employee ID"
                              className="transition-all duration-200 focus:ring-2 focus:ring-education-blue-500"
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Password Field */}
                    <FormField
                      control={form.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Password</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input
                                {...field}
                                type={showPassword ? "text" : "password"}
                                placeholder="Enter your password"
                                className="pr-10 transition-all duration-200 focus:ring-2 focus:ring-education-blue-500"
                                disabled={isLoading}
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                onClick={() => setShowPassword(!showPassword)}
                                disabled={isLoading}
                              >
                                {showPassword ? (
                                  <EyeOff className="h-4 w-4 text-gray-400" />
                                ) : (
                                  <Eye className="h-4 w-4 text-gray-400" />
                                )}
                                <span className="sr-only">
                                  {showPassword ? "Hide password" : "Show password"}
                                </span>
                              </Button>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Remember Me and Forgot Password */}
                    <div className="flex items-center justify-between">
                      <FormField
                        control={form.control}
                        name="rememberMe"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel className="text-sm font-normal cursor-pointer">
                                Remember me
                              </FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />
                      
                      <Button
                        type="button"
                        variant="link"
                        className="px-0 font-normal text-education-blue-600 hover:text-education-blue-700 dark:text-education-blue-400 dark:hover:text-education-blue-300"
                        disabled={isLoading}
                      >
                        Forgot password?
                      </Button>
                    </div>

                    {/* Submit Button */}
                    <Button
                      type="submit"
                      className="w-full bg-education-blue-600 hover:bg-education-blue-700 text-white transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>Signing in...</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <LogIn className="w-4 h-4" />
                          <span>Sign In</span>
                        </div>
                      )}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="text-center py-4 text-sm text-gray-500 dark:text-gray-400">
        <p>© 2024 Tanauan School of Arts and Trade. All rights reserved.</p>
      </footer>
    </div>
  )
}
